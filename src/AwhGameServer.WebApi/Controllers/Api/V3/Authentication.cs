using Microsoft.AspNetCore.Mvc;
using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Authentication;

namespace AwhGameServer.WebApi.Controllers.Api.V3;

[ApiController]
[Route("api/v3/authentication")]
public class Authentication : Controller
{
    [HttpPost("login")]
    public async Task<IActionResult> Login(
        [FromBody] LoginRequest request, 
        LoginCommandHandler authCommandHandler)
    {
        var loginCommand = request.MapToApplicationCommand();

        var loginCommandResult = await authCommandHandler.Handle(loginCommand, CancellationToken.None);
        
        var response = loginCommandResult.MapToContract();
        
        return Ok(response);
    }
    
    [HttpPost("refresh")]
    public async Task<IActionResult> Refresh(
        [FromBody] RefreshRequest request,
        RefreshCommandHandler refreshCommandHandler)
    {
        var refreshCommand = request.MapToApplicationCommand();
        
        var refreshCommandResult = await refreshCommandHandler.Handle(refreshCommand, CancellationToken.None);
        
        var response = refreshCommandResult.MapToContract();
        
        return Ok(response);
    }
}
