using AwhGameServer.Infrastructure.Extensions;
using AwhGameServer.WebApi.Extensions;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration.AddEnvironmentVariablesAliasesFromJson();
    
builder.Services
    .AddApplication()
    .AddInfrastructure(builder.Configuration)
    .AddWebApi(builder.Configuration);

var app = builder.Build();

app.UseWebApi();

app.Run();

public partial class Program;
