using AwhGameServer.Contracts.Api.V3.Models;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models;

public static class AuthIdentityMapper
{
    public static Application.Dto.AuthIdentityDto MapToApplicationDto(this AuthIdentity authIdentity)
    {
        return new Application.Dto.AuthIdentityDto(authIdentity.AuthMethod, authIdentity.AuthToken);
    }
    
    public static AuthIdentity MapToContract(this Application.Dto.AuthIdentityDto authIdentityDto)
    {
        return new AuthIdentity
        {
            AuthMethod = authIdentityDto.AuthMethod,
            AuthToken = authIdentityDto.AuthToken
        };
    }
}
