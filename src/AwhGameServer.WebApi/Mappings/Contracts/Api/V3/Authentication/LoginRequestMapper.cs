using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Authentication;

public static class LoginRequestMapper
{
    public static LoginCommand MapToApplicationCommand(this LoginRequest loginRequest)
    {
        return new LoginCommand(loginRequest.AuthIdentities.Select(x => x.MapToApplicationDto()).ToList());
    }
}
