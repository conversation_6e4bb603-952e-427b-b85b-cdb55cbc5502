using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Authentication;

public static class LoginResponseMapper
{
    public static LoginResponse MapToContract(this LoginCommandResult loginCommandResult)
    {
        return new LoginResponse
        {
            AuthTokens = loginCommandResult.AuthTokens.MapToContract()
        };
    }
}
