using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Authentication;

public static class RefreshResponseMapper
{
    public static RefreshResponse MapToContract(this RefreshCommandResult refreshCommandResult)
    {
        return new RefreshResponse
        {
            AuthTokens = refreshCommandResult.AuthTokens.MapToContract()
        };
    }
}
