using FluentValidation;
using AwhGameServer.Contracts.Api.V3.Authentication;

namespace AwhGameServer.WebApi.Validation.Contracts.Api.V3.Authentication;

public class LoginRequestValidator : AbstractValidator<LoginRequest>
{
    public LoginRequestValidator()
    {
        RuleFor(x => x.AuthIdentities)
            .NotNull()
            .NotEmpty()
            .Must(x => x.Length > 0)
            .WithMessage("Auth identities cannot be null or empty");
    }
}
